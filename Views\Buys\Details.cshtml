﻿@model Midterm0446.Models.Buy

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Buy</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.buy_date)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.buy_date)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Customer)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Customer.cus_id)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.buy_id">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
