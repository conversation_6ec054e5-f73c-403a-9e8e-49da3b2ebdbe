﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Midterm0446.Models;

#nullable disable

namespace Midterm0446.Migrations
{
    [DbContext(typeof(Seagame0446Context))]
    partial class Seagame0446ContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.16")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Midterm0446.Models.Buy", b =>
                {
                    b.Property<string>("buy_id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("buy_date")
                        .HasColumnType("datetime2");

                    b.Property<string>("cus_id")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("buy_id");

                    b.HasIndex("cus_id");

                    b.ToTable("Buys");
                });

            modelBuilder.Entity("Midterm0446.Models.BuyDetail", b =>
                {
                    b.Property<string>("buy_id")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnOrder(0);

                    b.Property<string>("match_id")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnOrder(1);

                    b.Property<int>("quantity")
                        .HasColumnType("int");

                    b.HasKey("buy_id", "match_id");

                    b.HasIndex("match_id");

                    b.ToTable("BuyDetails");
                });

            modelBuilder.Entity("Midterm0446.Models.Customer", b =>
                {
                    b.Property<string>("cus_id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("address")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("cus_name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("phone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("cus_id");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("Midterm0446.Models.Match", b =>
                {
                    b.Property<string>("match_id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("match_date")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("match_price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("match_quantity")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("match_time")
                        .HasColumnType("time");

                    b.Property<int>("sport_id")
                        .HasColumnType("int");

                    b.Property<string>("stadium")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("team1")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("team2")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("match_id");

                    b.HasIndex("sport_id");

                    b.ToTable("Matches");
                });

            modelBuilder.Entity("Midterm0446.Models.Sport", b =>
                {
                    b.Property<int>("sport_id")
                        .HasColumnType("int");

                    b.Property<string>("host_country")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("poster")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("sport_name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("start_date")
                        .HasColumnType("datetime2");

                    b.HasKey("sport_id");

                    b.ToTable("Sports");
                });

            modelBuilder.Entity("Midterm0446.Models.Buy", b =>
                {
                    b.HasOne("Midterm0446.Models.Customer", "Customer")
                        .WithMany("Buys")
                        .HasForeignKey("cus_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Midterm0446.Models.BuyDetail", b =>
                {
                    b.HasOne("Midterm0446.Models.Buy", "Buy")
                        .WithMany("BuyDetails")
                        .HasForeignKey("buy_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Midterm0446.Models.Match", "Match")
                        .WithMany("BuyDetails")
                        .HasForeignKey("match_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Buy");

                    b.Navigation("Match");
                });

            modelBuilder.Entity("Midterm0446.Models.Match", b =>
                {
                    b.HasOne("Midterm0446.Models.Sport", "Sport")
                        .WithMany("Matches")
                        .HasForeignKey("sport_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Sport");
                });

            modelBuilder.Entity("Midterm0446.Models.Buy", b =>
                {
                    b.Navigation("BuyDetails");
                });

            modelBuilder.Entity("Midterm0446.Models.Customer", b =>
                {
                    b.Navigation("Buys");
                });

            modelBuilder.Entity("Midterm0446.Models.Match", b =>
                {
                    b.Navigation("BuyDetails");
                });

            modelBuilder.Entity("Midterm0446.Models.Sport", b =>
                {
                    b.Navigation("Matches");
                });
#pragma warning restore 612, 618
        }
    }
}
