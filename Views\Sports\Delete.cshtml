﻿@model Midterm0446.Models.Sport

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Sport</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.sport_name)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.sport_name)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.start_date)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.start_date)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.poster)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.poster)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.host_country)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.host_country)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="sport_id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
