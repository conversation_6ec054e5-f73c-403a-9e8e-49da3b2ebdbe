﻿@model Midterm0446.Models.Match

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Match</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.match_date)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.match_date)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.match_time)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.match_time)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.match_price)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.match_price)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.match_quantity)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.match_quantity)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.stadium)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.stadium)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.team1)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.team1)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.team2)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.team2)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Sport)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Sport.sport_id)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="match_id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
