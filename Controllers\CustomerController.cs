using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Midterm0446.Models;

namespace Midterm0446.Controllers
{
    public class CustomerController : Controller
    {
        private readonly Seagame0446Context _context;

        public CustomerController(Seagame0446Context context)
        {
            _context = context;
        }

        public IActionResult Login()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(string phone, string password)
        {
            if (string.IsNullOrEmpty(phone) || string.IsNullOrEmpty(password))
            {
                ViewBag.ErrorMessage = "Please enter both phone and password.";
                return View();
            }

            var customer = await _context.Customers
                .FirstOrDefaultAsync(c => c.phone == phone && c.password == password);

            if (customer != null)
            {
                HttpContext.Session.SetString("CustomerId", customer.cus_id);
                HttpContext.Session.SetString("CustomerName", customer.cus_name);
                return RedirectToAction("Index", "Sports");
            }
            else
            {
                ViewBag.ErrorMessage = "Invalid phone or password.";
                return View();
            }
        }

        public IActionResult Register()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Register([Bind("cus_name,password,address,phone")] Customer customer, string confirmPassword)
        {
            if (string.IsNullOrEmpty(confirmPassword) || customer.password != confirmPassword)
            {
                ViewBag.ErrorMessage = "Passwords do not match.";
                return View(customer);
            }

            if (await _context.Customers.AnyAsync(c => c.phone == customer.phone))
            {
                ViewBag.ErrorMessage = "Phone number already exists.";
                return View(customer);
            }

            if (string.IsNullOrEmpty(customer.cus_name) || string.IsNullOrEmpty(customer.password) ||
                string.IsNullOrEmpty(customer.address) || string.IsNullOrEmpty(customer.phone))
            {
                ViewBag.ErrorMessage = "All fields are required.";
                return View(customer);
            }

            var lastCustomer = await _context.Customers
                .OrderByDescending(c => c.cus_id)
                .FirstOrDefaultAsync();

            int nextId = 1;
            if (lastCustomer != null && int.TryParse(lastCustomer.cus_id, out int lastId))
            {
                nextId = lastId + 1;
            }

            customer.cus_id = nextId.ToString();
            _context.Add(customer);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Login));
        }

        public IActionResult Logout()
        {
            HttpContext.Session.Clear();
            return RedirectToAction("Index", "Sports");
        }
    }
}
