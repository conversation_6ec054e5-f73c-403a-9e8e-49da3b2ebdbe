﻿@model Midterm0446.Models.Match

@{
    ViewData["Title"] = "Edit";
}

<h1>Edit</h1>

<h4>Match</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="match_id" />
            <div class="form-group">
                <label asp-for="sport_id" class="control-label"></label>
                <select asp-for="sport_id" class="form-control" asp-items="ViewBag.sport_id"></select>
                <span asp-validation-for="sport_id" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="match_date" class="control-label"></label>
                <input asp-for="match_date" class="form-control" />
                <span asp-validation-for="match_date" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="match_time" class="control-label"></label>
                <input asp-for="match_time" class="form-control" />
                <span asp-validation-for="match_time" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="match_price" class="control-label"></label>
                <input asp-for="match_price" class="form-control" />
                <span asp-validation-for="match_price" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="match_quantity" class="control-label"></label>
                <input asp-for="match_quantity" class="form-control" />
                <span asp-validation-for="match_quantity" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="stadium" class="control-label"></label>
                <input asp-for="stadium" class="form-control" />
                <span asp-validation-for="stadium" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="team1" class="control-label"></label>
                <input asp-for="team1" class="form-control" />
                <span asp-validation-for="team1" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="team2" class="control-label"></label>
                <input asp-for="team2" class="form-control" />
                <span asp-validation-for="team2" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
