﻿@model IEnumerable<Midterm0446.Models.Sport>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.sport_name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.start_date)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.poster)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.host_country)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.sport_name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.start_date)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.poster)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.host_country)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.sport_id">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.sport_id">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.sport_id">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
