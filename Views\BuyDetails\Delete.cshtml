﻿@model Midterm0446.Models.BuyDetail

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>BuyDetail</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.quantity)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.quantity)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Buy)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Buy.buy_id)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Match)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Match.match_id)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="buy_id" />
        <input type="hidden" asp-for="match_id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
