﻿@model Midterm0446.Models.BuyDetail

@{
    ViewData["Title"] = "Edit";
}

<h1>Edit</h1>

<h4>BuyDetail</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="buy_id" />
            <input type="hidden" asp-for="match_id" />
            <div class="form-group">
                <label asp-for="quantity" class="control-label"></label>
                <input asp-for="quantity" class="form-control" />
                <span asp-validation-for="quantity" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
