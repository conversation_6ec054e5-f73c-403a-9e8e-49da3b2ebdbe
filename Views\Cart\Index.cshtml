@model List<Midterm0446.Controllers.CartItemViewModel>

@{
    ViewData["Title"] = "Your Ticket";
}

<style>
    .cart-container {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
    }
    
    .cart-header {
        background-color: #343a40;
        color: white;
        padding: 10px;
        margin-bottom: 20px;
    }
    
    .cart-table {
        background-color: white;
        border-radius: 5px;
        overflow: hidden;
    }
    
    .quantity-input {
        width: 80px;
        text-align: center;
    }
    
    .cart-summary {
        background-color: white;
        padding: 15px;
        border-radius: 5px;
        margin-top: 20px;
    }
    
    .summary-text {
        color: #dc3545;
        font-weight: bold;
    }
    
    .action-buttons {
        text-align: right;
        margin-top: 20px;
    }
</style>

<div class="cart-container">
    <h2>Your Ticket</h2>
    
    @if (Model.Any())
    {
        <table class="table cart-table">
            <thead class="table-light">
                <tr>
                    <th>Match ID</th>
                    <th>Sport Id</th>
                    <th>Price</th>
                    <th>Quantity</th>
                    <th>Total</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model)
                {
                    <tr>
                        <td>@item.MatchId</td>
                        <td>@item.SportId</td>
                        <td>@item.Price.ToString("N0") VND</td>
                        <td>
                            <form asp-action="UpdateQuantity" method="post" style="display: inline;">
                                <input type="hidden" name="matchId" value="@item.MatchId" />
                                <input type="number" name="quantity" value="@item.Quantity" min="0" max="100" class="form-control quantity-input" onchange="this.form.submit()" />
                            </form>
                        </td>
                        <td>@item.Total.ToString("N0") VND</td>
                        <td>
                            <form asp-action="RemoveFromCart" method="post" style="display: inline;">
                                <input type="hidden" name="matchId" value="@item.MatchId" />
                                <button type="submit" class="btn btn-link text-primary">Delete</button>
                            </form>
                            <button type="button" class="btn btn-success btn-sm">Update</button>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
        
        <div class="cart-summary">
            <div class="row">
                <div class="col-md-6">
                    <span class="summary-text">Type count: @ViewBag.TypeCount</span>
                </div>
                <div class="col-md-6 text-end">
                    <span class="summary-text">Quantity: @ViewBag.TotalQuantity</span>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-12 text-end">
                    <span class="summary-text">Total: @ViewBag.TotalAmount.ToString("N0") VND</span>
                </div>
            </div>
        </div>
        
        <div class="action-buttons">
            <a asp-action="ClearAll" class="btn btn-outline-secondary">Clear All</a>
            <a asp-action="PlaceOrder" class="btn btn-primary">Place an Order</a>
        </div>
    }
    else
    {
        <div class="text-center">
            <p>Your cart is empty.</p>
            <a asp-controller="Matches" asp-action="Index" class="btn btn-primary">Continue Shopping</a>
        </div>
    }
</div>
