using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Midterm0446.Models;

namespace Midterm0446.Controllers
{
    public class StatisticsController : Controller
    {
        private readonly Seagame0446Context _context;

        public StatisticsController(Seagame0446Context context)
        {
            _context = context;
        }

        public async Task<IActionResult> TicketCount()
        {
            var ticketStats = await _context.BuyDetails
                .Include(bd => bd.Match)
                .ThenInclude(m => m.Sport)
                .GroupBy(bd => bd.Match.Sport.sport_name)
                .Select(g => new
                {
                    SportName = g.Key,
                    TotalTickets = g.Sum(bd => bd.quantity)
                })
                .OrderByDescending(x => x.TotalTickets)
                .ToListAsync();

            return View(ticketStats);
        }

        public async Task<IActionResult> Revenue()
        {
            var revenueStats = await _context.BuyDetails
                .Include(bd => bd.Match)
                .ThenInclude(m => m.Sport)
                .GroupBy(bd => bd.Match.Sport.sport_name)
                .Select(g => new
                {
                    SportName = g.Key,
                    TotalRevenue = g.Sum(bd => bd.quantity * bd.Match.match_price)
                })
                .OrderByDescending(x => x.TotalRevenue)
                .ToListAsync();

            return View(revenueStats);
        }
    }
}
