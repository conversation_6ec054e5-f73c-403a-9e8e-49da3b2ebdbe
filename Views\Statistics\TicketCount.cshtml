@model IEnumerable<dynamic>

@{
    ViewData["Title"] = "Count of Ticket";
}

<h2>Count of Ticket</h2>

<table class="table table-striped">
    <thead>
        <tr>
            <th>Movie name</th>
            <th>Count of ticket</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.SportName</td>
                <td>@item.TotalTickets</td>
            </tr>
        }
    </tbody>
</table>

@if (!Model.Any())
{
    <div class="alert alert-info">
        <p>No ticket sales data available.</p>
    </div>
}
