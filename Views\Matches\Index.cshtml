﻿@model IEnumerable<Midterm0446.Models.Match>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.match_date)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.match_time)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.match_price)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.match_quantity)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.stadium)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.team1)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.team2)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Sport)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.match_date)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.match_time)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.match_price)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.match_quantity)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.stadium)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.team1)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.team2)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Sport.sport_id)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.match_id">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.match_id">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.match_id">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
