﻿@model IEnumerable<Midterm0446.Models.Match>

@{
    ViewData["Title"] = "Match Schedule";
}

<h2>Match Schedule</h2>

<table class="table table-striped">
    <thead class="table-dark">
        <tr>
            <th>Match ID</th>
            <th>Sport</th>
            <th>Teams</th>
            <th>Date</th>
            <th>Time</th>
            <th>Stadium</th>
            <th>Price</th>
            <th>Tickets Left</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            var soldTickets = item.BuyDetails?.Sum(bd => bd.quantity) ?? 0;
            var ticketsLeft = item.match_quantity - soldTickets;
            var matchDateTime = item.match_date.Date.Add(item.match_time);

            string status;
            string buttonClass;
            string buttonText;

            if (matchDateTime < DateTime.Now)
            {
                status = "Finish";
                buttonClass = "btn-secondary";
                buttonText = "Finish";
            }
            else if (ticketsLeft <= 0)
            {
                status = "Sold out";
                buttonClass = "btn-danger";
                buttonText = "Sold out";
            }
            else
            {
                status = "Buy";
                buttonClass = "btn-success";
                buttonText = "Buy";
            }

            <tr>
                <td>@item.match_id</td>
                <td>@item.Sport.sport_name</td>
                <td>@item.team1 vs @item.team2</td>
                <td>@item.match_date.ToString("dd/MM/yyyy")</td>
                <td>@item.match_time.ToString(@"hh\:mm")</td>
                <td>@item.stadium</td>
                <td>@item.match_price.ToString("N0") VND</td>
                <td>@ticketsLeft</td>
                <td>
                    @if (status == "Buy")
                    {
                        <form asp-controller="Cart" asp-action="AddToCart" method="post" style="display: inline;">
                            <input type="hidden" name="matchId" value="@item.match_id" />
                            <input type="hidden" name="quantity" value="1" />
                            <button type="submit" class="btn @buttonClass btn-sm">@buttonText</button>
                        </form>
                    }
                    else
                    {
                        <button class="btn @buttonClass btn-sm" disabled>@buttonText</button>
                    }
                </td>
            </tr>
        }
    </tbody>
</table>
