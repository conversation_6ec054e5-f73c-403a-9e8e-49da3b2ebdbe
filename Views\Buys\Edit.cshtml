﻿@model Midterm0446.Models.Buy

@{
    ViewData["Title"] = "Edit";
}

<h1>Edit</h1>

<h4>Buy</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="buy_id" />
            <div class="form-group">
                <label asp-for="buy_date" class="control-label"></label>
                <input asp-for="buy_date" class="form-control" />
                <span asp-validation-for="buy_date" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="cus_id" class="control-label"></label>
                <select asp-for="cus_id" class="form-control" asp-items="ViewBag.cus_id"></select>
                <span asp-validation-for="cus_id" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
