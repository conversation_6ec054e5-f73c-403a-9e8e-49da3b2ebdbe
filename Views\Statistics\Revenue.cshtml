@model IEnumerable<dynamic>

@{
    ViewData["Title"] = "Revenue Statistics";
}

<h2>Revenue Statistics</h2>

<table class="table table-striped">
    <thead>
        <tr>
            <th>Movie name</th>
            <th>Revenue total</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.SportName</td>
                <td>@item.TotalRevenue.ToString("N0") VND</td>
            </tr>
        }
    </tbody>
</table>

@if (!Model.Any())
{
    <div class="alert alert-info">
        <p>No revenue data available.</p>
    </div>
}
