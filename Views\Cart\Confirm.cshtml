@{
    ViewData["Title"] = "Done! Bought Ticket";
}

<style>
    .confirmation-container {
        background-color: #f8f9fa;
        padding: 40px;
        border-radius: 8px;
        margin: 50px auto;
        max-width: 600px;
        text-align: center;
    }
    
    .success-message {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .back-link {
        color: #007bff;
        text-decoration: none;
        font-size: 14px;
    }
    
    .back-link:hover {
        text-decoration: underline;
    }
</style>

<div class="confirmation-container">
    <div class="success-message">
        <h2>Done! Bought Ticket</h2>
        <p>Your ticket purchase has been confirmed successfully.</p>
    </div>
    
    <p><a href="@Url.Action("Index", "Sports")" class="back-link">Back to Seagame</a></p>
</div>
