@model Midterm0446.Models.Customer

@{
    ViewData["Title"] = "Register";
}

<div class="row justify-content-center">
    <div class="col-md-6">
        <h2>Register</h2>
        <h5>Customer</h5>
        
        @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
        {
            <div class="alert alert-danger">
                @ViewBag.ErrorMessage
            </div>
        }

        <form asp-action="Register" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>

            <div class="form-group mb-3">
                <label asp-for="cus_name" class="form-label">Full name</label>
                <input asp-for="cus_name" class="form-control" placeholder="Customer1" required />
                <span asp-validation-for="cus_name" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="address" class="form-label">Address</label>
                <input asp-for="address" class="form-control" placeholder="<EMAIL>" required />
                <span asp-validation-for="address" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="phone" class="form-label">Phone</label>
                <input asp-for="phone" class="form-control" placeholder="0987654321" required />
                <span asp-validation-for="phone" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label asp-for="password" class="form-label">Password</label>
                <input asp-for="password" type="password" class="form-control" placeholder="••••••" required />
                <span asp-validation-for="password" class="text-danger"></span>
            </div>

            <div class="form-group mb-3">
                <label for="confirmPassword" class="form-label">Confirm password</label>
                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" placeholder="••••••" required />
            </div>

            <div class="form-group mb-3">
                <button type="submit" class="btn btn-primary">Register</button>
            </div>
        </form>

        <div class="mt-3">
            <a asp-action="Index" asp-controller="Sports">Back to List</a>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
