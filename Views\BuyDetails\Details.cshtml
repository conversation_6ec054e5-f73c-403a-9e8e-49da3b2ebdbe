﻿@model Midterm0446.Models.BuyDetail

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>BuyDetail</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.quantity)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.quantity)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Buy)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Buy.buy_id)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Match)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Match.match_id)
        </dd>
    </dl>
</div>
<div>
    @Html.ActionLink("Edit", "Edit", new { /* id = Model.PrimaryKey */ }) |
    <a asp-action="Index">Back to List</a>
</div>
