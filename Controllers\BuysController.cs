﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Midterm0446.Models;

namespace Midterm0446.Controllers
{
    public class BuysController : Controller
    {
        private readonly Seagame0446Context _context;

        public BuysController(Seagame0446Context context)
        {
            _context = context;
        }

        // GET: Buys
        public async Task<IActionResult> Index()
        {
            var seagame0446Context = _context.Buys.Include(b => b.Customer);
            return View(await seagame0446Context.ToListAsync());
        }

        // GET: Buys/Details/5
        public async Task<IActionResult> Details(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var buy = await _context.Buys
                .Include(b => b.Customer)
                .FirstOrDefaultAsync(m => m.buy_id == id);
            if (buy == null)
            {
                return NotFound();
            }

            return View(buy);
        }

        // GET: Buys/Create
        public IActionResult Create()
        {
            ViewData["cus_id"] = new SelectList(_context.Customers, "cus_id", "cus_id");
            return View();
        }

        // POST: Buys/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("buy_id,buy_date,cus_id")] Buy buy)
        {
            if (ModelState.IsValid)
            {
                _context.Add(buy);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            ViewData["cus_id"] = new SelectList(_context.Customers, "cus_id", "cus_id", buy.cus_id);
            return View(buy);
        }

        // GET: Buys/Edit/5
        public async Task<IActionResult> Edit(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var buy = await _context.Buys.FindAsync(id);
            if (buy == null)
            {
                return NotFound();
            }
            ViewData["cus_id"] = new SelectList(_context.Customers, "cus_id", "cus_id", buy.cus_id);
            return View(buy);
        }

        // POST: Buys/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(string id, [Bind("buy_id,buy_date,cus_id")] Buy buy)
        {
            if (id != buy.buy_id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(buy);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!BuyExists(buy.buy_id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["cus_id"] = new SelectList(_context.Customers, "cus_id", "cus_id", buy.cus_id);
            return View(buy);
        }

        // GET: Buys/Delete/5
        public async Task<IActionResult> Delete(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var buy = await _context.Buys
                .Include(b => b.Customer)
                .FirstOrDefaultAsync(m => m.buy_id == id);
            if (buy == null)
            {
                return NotFound();
            }

            return View(buy);
        }

        // POST: Buys/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(string id)
        {
            var buy = await _context.Buys.FindAsync(id);
            if (buy != null)
            {
                _context.Buys.Remove(buy);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool BuyExists(string id)
        {
            return _context.Buys.Any(e => e.buy_id == id);
        }
    }
}
