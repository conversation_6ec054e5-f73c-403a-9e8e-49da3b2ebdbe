@model IEnumerable<Midterm0446.Models.Sport>

@{
    ViewData["Title"] = "Ongoing Sports";
}

<h2>Ongoing Sports</h2>

<div class="row">
    @foreach (var item in Model)
    {
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <h5 class="card-title">@item.sport_name</h5>
                    <div class="mb-3">
                        <img src="@item.poster" alt="@item.sport_name" class="img-fluid" style="max-height: 150px;" />
                    </div>
                    <p class="card-text">
                        <strong>Start date:</strong> @item.start_date.ToString("dd/MM/yyyy")<br />
                        <strong>Host country:</strong> @item.host_country
                    </p>
                    <button class="btn btn-success btn-sm">Ongoing</button>
                </div>
            </div>
        </div>
    }
</div>

@if (!Model.Any())
{
    <div class="alert alert-info">
        <h4>No ongoing sports found</h4>
        <p>There are currently no sports that have started.</p>
    </div>
}

<div class="d-flex justify-content-between align-items-center mt-4">
    <div>
        <label for="pageSize">Size of page:</label>
        <select id="pageSize" onchange="changePageSize(this.value)" class="form-select d-inline-block" style="width: auto;">
            <option value="3" selected="@(ViewBag.PageSize == 3 ? "selected" : null)">3</option>
            <option value="6" selected="@(ViewBag.PageSize == 6 ? "selected" : null)">6</option>
            <option value="9" selected="@(ViewBag.PageSize == 9 ? "selected" : null)">9</option>
        </select>
    </div>
    
    <div>
        Page: @ViewBag.CurrentPage/@ViewBag.TotalPages
    </div>
</div>

<nav aria-label="Page navigation" class="mt-3">
    <ul class="pagination justify-content-center">
        @if (ViewBag.CurrentPage > 1)
        {
            <li class="page-item">
                <a class="page-link" href="?page=@(ViewBag.CurrentPage - 1)&pageSize=@ViewBag.PageSize">‹</a>
            </li>
        }
        
        @for (int i = 1; i <= ViewBag.TotalPages; i++)
        {
            <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                <a class="page-link" href="?page=@i&pageSize=@ViewBag.PageSize">@i</a>
            </li>
        }
        
        @if (ViewBag.CurrentPage < ViewBag.TotalPages)
        {
            <li class="page-item">
                <a class="page-link" href="?page=@(ViewBag.CurrentPage + 1)&pageSize=@ViewBag.PageSize">›</a>
            </li>
        }
    </ul>
</nav>

<script>
function changePageSize(pageSize) {
    window.location.href = '?page=1&pageSize=' + pageSize;
}
</script>
