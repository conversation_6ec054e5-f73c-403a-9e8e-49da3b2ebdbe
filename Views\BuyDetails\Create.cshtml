﻿@model Midterm0446.Models.BuyDetail

@{
    ViewData["Title"] = "Create";
}

<h1>Create</h1>

<h4>BuyDetail</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="buy_id" class="control-label"></label>
                <select asp-for="buy_id" class ="form-control" asp-items="ViewBag.buy_id"></select>
            </div>
            <div class="form-group">
                <label asp-for="match_id" class="control-label"></label>
                <select asp-for="match_id" class ="form-control" asp-items="ViewBag.match_id"></select>
            </div>
            <div class="form-group">
                <label asp-for="quantity" class="control-label"></label>
                <input asp-for="quantity" class="form-control" />
                <span asp-validation-for="quantity" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
