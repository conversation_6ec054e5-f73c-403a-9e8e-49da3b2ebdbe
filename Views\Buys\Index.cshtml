﻿@model IEnumerable<Midterm0446.Models.Buy>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.buy_date)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Customer)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.buy_date)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Customer.cus_id)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.buy_id">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.buy_id">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.buy_id">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
