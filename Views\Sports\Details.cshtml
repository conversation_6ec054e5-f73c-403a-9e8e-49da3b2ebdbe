﻿@model Midterm0446.Models.Sport

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Sport</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.sport_name)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.sport_name)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.start_date)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.start_date)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.poster)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.poster)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.host_country)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.host_country)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.sport_id">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
