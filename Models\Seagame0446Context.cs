using Microsoft.EntityFrameworkCore;

namespace Midterm0446.Models
{
    public class Seagame0446Context : DbContext
    {
        public Seagame0446Context(DbContextOptions<Seagame0446Context> options) : base(options)
        {
        }

        public DbSet<Sport> Sports { get; set; }
        public DbSet<Match> Matches { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Buy> Buys { get; set; }
        public DbSet<BuyDetail> BuyDetails { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Sport>()
                .Property(s => s.sport_id)
                .ValueGeneratedNever();

            modelBuilder.Entity<Match>()
                .Property(m => m.match_id)
                .ValueGeneratedNever();

            modelBuilder.Entity<Customer>()
                .Property(c => c.cus_id)
                .ValueGeneratedNever();

            modelBuilder.Entity<Buy>()
                .Property(b => b.buy_id)
                .ValueGeneratedNever();

            modelBuilder.Entity<BuyDetail>()
                .HasKey(bd => new { bd.buy_id, bd.match_id });

            modelBuilder.Entity<BuyDetail>()
                .Property(bd => bd.buy_id)
                .ValueGeneratedNever();

            modelBuilder.Entity<BuyDetail>()
                .Property(bd => bd.match_id)
                .ValueGeneratedNever();

            modelBuilder.Entity<BuyDetail>()
                .HasOne(bd => bd.Buy)
                .WithMany(b => b.BuyDetails)
                .HasForeignKey(bd => bd.buy_id);

            modelBuilder.Entity<BuyDetail>()
                .HasOne(bd => bd.Match)
                .WithMany(m => m.BuyDetails)
                .HasForeignKey(bd => bd.match_id);

            modelBuilder.Entity<Match>()
                .HasOne(m => m.Sport)
                .WithMany(s => s.Matches)
                .HasForeignKey(m => m.sport_id);

            modelBuilder.Entity<Buy>()
                .HasOne(b => b.Customer)
                .WithMany(c => c.Buys)
                .HasForeignKey(b => b.cus_id);

            base.OnModelCreating(modelBuilder);
        }
    }
}
