using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Midterm0446.Models;
using System.Text.Json;

namespace Midterm0446.Controllers
{
    public class CartController : Controller
    {
        private readonly Seagame0446Context _context;

        public CartController(Seagame0446Context context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var cartItems = GetCartItems();
            var cartViewModels = new List<CartItemViewModel>();

            foreach (var item in cartItems)
            {
                var match = await _context.Matches
                    .Include(m => m.Sport)
                    .FirstOrDefaultAsync(m => m.match_id == item.MatchId);
                
                if (match != null)
                {
                    cartViewModels.Add(new CartItemViewModel
                    {
                        MatchId = match.match_id,
                        SportId = match.sport_id,
                        Price = match.match_price,
                        Quantity = item.Quantity,
                        Total = match.match_price * item.Quantity
                    });
                }
            }

            ViewBag.TypeCount = cartViewModels.Count;
            ViewBag.TotalQuantity = cartViewModels.Sum(c => c.Quantity);
            ViewBag.TotalAmount = cartViewModels.Sum(c => c.Total);

            return View(cartViewModels);
        }

        [HttpPost]
        public IActionResult AddToCart(string matchId, int quantity = 1)
        {
            var cartItems = GetCartItems();
            var existingItem = cartItems.FirstOrDefault(c => c.MatchId == matchId);

            if (existingItem != null)
            {
                existingItem.Quantity += quantity;
            }
            else
            {
                cartItems.Add(new CartItem { MatchId = matchId, Quantity = quantity });
            }

            SaveCartItems(cartItems);
            return RedirectToAction("Index");
        }

        [HttpPost]
        public IActionResult UpdateQuantity(string matchId, int quantity)
        {
            var cartItems = GetCartItems();
            var item = cartItems.FirstOrDefault(c => c.MatchId == matchId);

            if (item != null)
            {
                if (quantity > 0)
                {
                    item.Quantity = quantity;
                }
                else
                {
                    cartItems.Remove(item);
                }
            }

            SaveCartItems(cartItems);
            return RedirectToAction("Index");
        }

        [HttpPost]
        public IActionResult RemoveFromCart(string matchId)
        {
            var cartItems = GetCartItems();
            var item = cartItems.FirstOrDefault(c => c.MatchId == matchId);

            if (item != null)
            {
                cartItems.Remove(item);
            }

            SaveCartItems(cartItems);
            return RedirectToAction("Index");
        }

        public IActionResult ClearAll()
        {
            HttpContext.Session.Remove("Cart");
            return RedirectToAction("Index");
        }

        public async Task<IActionResult> PlaceOrder()
        {
            var cartItems = GetCartItems();
            var cartViewModels = new List<CartItemViewModel>();

            foreach (var item in cartItems)
            {
                var match = await _context.Matches
                    .Include(m => m.Sport)
                    .FirstOrDefaultAsync(m => m.match_id == item.MatchId);
                
                if (match != null)
                {
                    cartViewModels.Add(new CartItemViewModel
                    {
                        MatchId = match.match_id,
                        SportId = match.sport_id,
                        Price = match.match_price,
                        Quantity = item.Quantity,
                        Total = match.match_price * item.Quantity
                    });
                }
            }

            ViewBag.TypeCount = cartViewModels.Count;
            ViewBag.TotalQuantity = cartViewModels.Sum(c => c.Quantity);
            ViewBag.TotalAmount = cartViewModels.Sum(c => c.Total);

            var customerId = HttpContext.Session.GetString("CustomerId");
            if (!string.IsNullOrEmpty(customerId))
            {
                var customer = await _context.Customers.FindAsync(customerId);
                ViewBag.Customer = customer;
            }

            return View(cartViewModels);
        }

        [HttpPost]
        public async Task<IActionResult> Confirm()
        {
            var customerId = HttpContext.Session.GetString("CustomerId");
            if (string.IsNullOrEmpty(customerId))
            {
                return RedirectToAction("Login", "Customer");
            }

            var cartItems = GetCartItems();
            if (!cartItems.Any())
            {
                return RedirectToAction("Index");
            }

            var lastBuy = await _context.Buys
                .OrderByDescending(b => b.buy_id)
                .FirstOrDefaultAsync();
            
            int nextBuyId = 1;
            if (lastBuy != null && int.TryParse(lastBuy.buy_id, out int lastId))
            {
                nextBuyId = lastId + 1;
            }

            var buy = new Buy
            {
                buy_id = nextBuyId.ToString(),
                buy_date = DateTime.Now,
                cus_id = customerId
            };

            _context.Buys.Add(buy);

            foreach (var item in cartItems)
            {
                var buyDetail = new BuyDetail
                {
                    buy_id = buy.buy_id,
                    match_id = item.MatchId,
                    quantity = item.Quantity
                };
                _context.BuyDetails.Add(buyDetail);
            }

            await _context.SaveChangesAsync();
            HttpContext.Session.Remove("Cart");

            return View();
        }

        private List<CartItem> GetCartItems()
        {
            var cartJson = HttpContext.Session.GetString("Cart");
            if (string.IsNullOrEmpty(cartJson))
            {
                return new List<CartItem>();
            }
            return JsonSerializer.Deserialize<List<CartItem>>(cartJson) ?? new List<CartItem>();
        }

        private void SaveCartItems(List<CartItem> cartItems)
        {
            var cartJson = JsonSerializer.Serialize(cartItems);
            HttpContext.Session.SetString("Cart", cartJson);
        }
    }

    public class CartItem
    {
        public string MatchId { get; set; }
        public int Quantity { get; set; }
    }

    public class CartItemViewModel
    {
        public string MatchId { get; set; }
        public int SportId { get; set; }
        public decimal Price { get; set; }
        public int Quantity { get; set; }
        public decimal Total { get; set; }
    }
}
