﻿@model Midterm0446.Models.Buy

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Buy</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.buy_date)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.buy_date)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Customer)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Customer.cus_id)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="buy_id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
