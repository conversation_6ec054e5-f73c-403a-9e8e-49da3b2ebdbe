﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Midterm0446.Models;

namespace Midterm0446.Controllers
{
    public class BuyDetailsController : Controller
    {
        private readonly Seagame0446Context _context;

        public BuyDetailsController(Seagame0446Context context)
        {
            _context = context;
        }

        // GET: BuyDetails
        public async Task<IActionResult> Index()
        {
            var seagame0446Context = _context.BuyDetails.Include(b => b.Buy).Include(b => b.Match);
            return View(await seagame0446Context.ToListAsync());
        }

        // GET: BuyDetails/Details/5
        public async Task<IActionResult> Details(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var buyDetail = await _context.BuyDetails
                .Include(b => b.Buy)
                .Include(b => b.Match)
                .FirstOrDefaultAsync(m => m.buy_id == id);
            if (buyDetail == null)
            {
                return NotFound();
            }

            return View(buyDetail);
        }

        // GET: BuyDetails/Create
        public IActionResult Create()
        {
            ViewData["buy_id"] = new SelectList(_context.Buys, "buy_id", "buy_id");
            ViewData["match_id"] = new SelectList(_context.Matches, "match_id", "match_id");
            return View();
        }

        // POST: BuyDetails/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("buy_id,match_id,quantity")] BuyDetail buyDetail)
        {
            if (ModelState.IsValid)
            {
                _context.Add(buyDetail);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            ViewData["buy_id"] = new SelectList(_context.Buys, "buy_id", "buy_id", buyDetail.buy_id);
            ViewData["match_id"] = new SelectList(_context.Matches, "match_id", "match_id", buyDetail.match_id);
            return View(buyDetail);
        }

        // GET: BuyDetails/Edit/5
        public async Task<IActionResult> Edit(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var buyDetail = await _context.BuyDetails.FindAsync(id);
            if (buyDetail == null)
            {
                return NotFound();
            }
            ViewData["buy_id"] = new SelectList(_context.Buys, "buy_id", "buy_id", buyDetail.buy_id);
            ViewData["match_id"] = new SelectList(_context.Matches, "match_id", "match_id", buyDetail.match_id);
            return View(buyDetail);
        }

        // POST: BuyDetails/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(string id, [Bind("buy_id,match_id,quantity")] BuyDetail buyDetail)
        {
            if (id != buyDetail.buy_id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(buyDetail);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!BuyDetailExists(buyDetail.buy_id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["buy_id"] = new SelectList(_context.Buys, "buy_id", "buy_id", buyDetail.buy_id);
            ViewData["match_id"] = new SelectList(_context.Matches, "match_id", "match_id", buyDetail.match_id);
            return View(buyDetail);
        }

        // GET: BuyDetails/Delete/5
        public async Task<IActionResult> Delete(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var buyDetail = await _context.BuyDetails
                .Include(b => b.Buy)
                .Include(b => b.Match)
                .FirstOrDefaultAsync(m => m.buy_id == id);
            if (buyDetail == null)
            {
                return NotFound();
            }

            return View(buyDetail);
        }

        // POST: BuyDetails/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(string id)
        {
            var buyDetail = await _context.BuyDetails.FindAsync(id);
            if (buyDetail != null)
            {
                _context.BuyDetails.Remove(buyDetail);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool BuyDetailExists(string id)
        {
            return _context.BuyDetails.Any(e => e.buy_id == id);
        }
    }
}
