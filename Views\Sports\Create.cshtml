﻿@model Midterm0446.Models.Sport

@{
    ViewData["Title"] = "Create";
}

<h1>Create</h1>

<h4>Sport</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="sport_id" class="control-label"></label>
                <input asp-for="sport_id" class="form-control" />
                <span asp-validation-for="sport_id" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="sport_name" class="control-label"></label>
                <input asp-for="sport_name" class="form-control" />
                <span asp-validation-for="sport_name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="start_date" class="control-label"></label>
                <input asp-for="start_date" class="form-control" />
                <span asp-validation-for="start_date" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="poster" class="control-label"></label>
                <input asp-for="poster" class="form-control" />
                <span asp-validation-for="poster" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="host_country" class="control-label"></label>
                <input asp-for="host_country" class="form-control" />
                <span asp-validation-for="host_country" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
