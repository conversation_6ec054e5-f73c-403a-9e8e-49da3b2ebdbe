@model List<Midterm0446.Controllers.CartItemViewModel>

@{
    ViewData["Title"] = "Place an order";
}

<style>
    .order-container {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
    }
    
    .order-table {
        background-color: white;
        border-radius: 5px;
        overflow: hidden;
        margin-bottom: 20px;
    }
    
    .order-summary {
        background-color: white;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .summary-text {
        color: #dc3545;
        font-weight: bold;
    }
    
    .customer-info {
        background-color: white;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .action-buttons {
        text-align: right;
    }
</style>

<div class="order-container">
    <h2>Place an order</h2>
    
    <table class="table order-table">
        <thead class="table-light">
            <tr>
                <th>Match ID</th>
                <th>Sport Id</th>
                <th>Price</th>
                <th>Quantity</th>
                <th>Total</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                <tr>
                    <td>@item.MatchId</td>
                    <td>@item.SportId</td>
                    <td>@item.Price.ToString("N0") VND</td>
                    <td>@item.Quantity</td>
                    <td>@item.Total.ToString("N0") VND</td>
                </tr>
            }
        </tbody>
    </table>
    
    <div class="order-summary">
        <div class="row">
            <div class="col-md-6">
                <span class="summary-text">Type count: @ViewBag.TypeCount</span>
            </div>
            <div class="col-md-6 text-end">
                <span class="summary-text">Quantity: @ViewBag.TotalQuantity</span>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-12 text-end">
                <span class="summary-text">Total: @ViewBag.TotalAmount.ToString("N0") VND</span>
            </div>
        </div>
    </div>
    
    <div class="customer-info">
        <h4>Ticket Information</h4>
        <hr />
        @if (ViewBag.Customer != null)
        {
            var customer = ViewBag.Customer as Midterm0446.Models.Customer;
            <dl class="row">
                <dt class="col-sm-3">Customer name:</dt>
                <dd class="col-sm-9">@customer.cus_name</dd>
                
                <dt class="col-sm-3">Address:</dt>
                <dd class="col-sm-9">@customer.address</dd>
                
                <dt class="col-sm-3">Phone:</dt>
                <dd class="col-sm-9">@customer.phone</dd>
                
                <dt class="col-sm-3">Buy date:</dt>
                <dd class="col-sm-9">@DateTime.Now.ToString("dd/MM/yyyy")</dd>
            </dl>
        }
        else
        {
            <p class="text-danger">Please login to continue.</p>
        }
    </div>
    
    <div class="action-buttons">
        <a asp-action="Index" class="btn btn-outline-secondary">Back to Cart</a>
        @if (ViewBag.Customer != null)
        {
            <form asp-action="Confirm" method="post" style="display: inline;">
                <button type="submit" class="btn btn-success">Confirm</button>
            </form>
        }
        else
        {
            <a asp-controller="Customer" asp-action="Login" class="btn btn-primary">Login</a>
        }
    </div>
</div>
